import jwt from 'jsonwebtoken';
import User from '../models/UserSQLite.js';
import Role from '../models/RoleSQLite.js';
import { executeQuery, getOne } from '../config/databaseSQLite.js';

// التحقق من صحة التوكن
export const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token required',
        message_ar: 'رمز الوصول مطلوب'
      });
    }

    // التحقق من صحة التوكن
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // التحقق من وجود الجلسة في قاعدة البيانات
    const sessionQuery = `
      SELECT * FROM user_sessions 
      WHERE user_id = ? AND token_hash = ? AND expires_at > datetime('now')
    `;
    
    const tokenHash = Buffer.from(token).toString('base64');
    const session = await getOne(sessionQuery, [decoded.userId, tokenHash]);
    
    if (!session) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired session',
        message_ar: 'جلسة غير صالحة أو منتهية الصلاحية'
      });
    }

    // الحصول على بيانات المستخدم مع الدور والصلاحيات
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not found',
        message_ar: 'المستخدم غير موجود'
      });
    }

    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated',
        message_ar: 'الحساب معطل'
      });
    }

    // إضافة بيانات المستخدم إلى الطلب
    req.user = user;
    req.session = session;
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token',
        message_ar: 'رمز غير صالح'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired',
        message_ar: 'انتهت صلاحية الرمز'
      });
    }

    console.error('Authentication error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication failed',
      message_ar: 'فشل في المصادقة'
    });
  }
};

// التحقق من الصلاحيات
export const requirePermission = (permission) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
          message_ar: 'المصادقة مطلوبة'
        });
      }

      let userPermissions = [];
      try {
        if (req.user.permissions) {
          if (typeof req.user.permissions === 'string') {
            userPermissions = JSON.parse(req.user.permissions);
          } else if (Array.isArray(req.user.permissions)) {
            userPermissions = req.user.permissions;
          }
        }
      } catch (error) {
        console.error('Error parsing user permissions:', error);
        userPermissions = [];
      }

      console.log('🔍 Permission check debug:');
      console.log('- Required permission:', permission);
      console.log('- User permissions:', userPermissions);
      console.log('- User permissions type:', typeof userPermissions);
      console.log('- User permissions length:', userPermissions.length);
      console.log('- User object keys:', Object.keys(req.user));
      console.log('- Raw user.permissions:', req.user.permissions);
      console.log('- hasPermission result:', Role.hasPermission(userPermissions, permission));

      if (!Role.hasPermission(userPermissions, permission)) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions',
          message_ar: 'صلاحيات غير كافية',
          required_permission: permission
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Permission check failed',
        message_ar: 'فشل في فحص الصلاحيات'
      });
    }
  };
};

// التحقق من دور المستخدم
export const requireRole = (roles) => {
  const roleArray = Array.isArray(roles) ? roles : [roles];
  
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
          message_ar: 'المصادقة مطلوبة'
        });
      }

      if (!roleArray.includes(req.user.role_name)) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient role privileges',
          message_ar: 'صلاحيات الدور غير كافية',
          required_roles: roleArray,
          user_role: req.user.role_name
        });
      }

      next();
    } catch (error) {
      console.error('Role check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Role check failed',
        message_ar: 'فشل في فحص الدور'
      });
    }
  };
};

// التحقق من ملكية المورد أو صلاحية المدير
export const requireOwnershipOrAdmin = (resourceUserIdField = 'user_id') => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
          message_ar: 'المصادقة مطلوبة'
        });
      }

      // المدير الأعلى والمدير لديهم صلاحية الوصول لكل شيء
      if (['super_admin', 'admin'].includes(req.user.role_name)) {
        return next();
      }

      // التحقق من الملكية
      const resourceUserId = req.body[resourceUserIdField] || req.params[resourceUserIdField];
      
      if (resourceUserId && parseInt(resourceUserId) === req.user.id) {
        return next();
      }

      return res.status(403).json({
        success: false,
        message: 'Access denied: insufficient privileges',
        message_ar: 'الوصول مرفوض: صلاحيات غير كافية'
      });
    } catch (error) {
      console.error('Ownership check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Access check failed',
        message_ar: 'فشل في فحص الوصول'
      });
    }
  };
};

// middleware لتسجيل النشاطات
export const logActivity = (action, resourceType = null) => {
  return async (req, res, next) => {
    try {
      // تنفيذ الطلب أولاً
      const originalSend = res.send;
      
      res.send = function(data) {
        // تسجيل النشاط بعد نجاح العملية
        if (res.statusCode >= 200 && res.statusCode < 300) {
          const logData = {
            user_id: req.user ? req.user.id : null,
            action: action,
            resource_type: resourceType,
            resource_id: req.params.id || null,
            details: JSON.stringify({
              method: req.method,
              url: req.originalUrl,
              body: req.method !== 'GET' ? req.body : null,
              query: req.query
            }),
            ip_address: req.ip || req.connection.remoteAddress,
            user_agent: req.get('User-Agent')
          };

          // تسجيل النشاط في قاعدة البيانات (بدون انتظار)
          executeQuery(
            `INSERT INTO activity_logs (user_id, action, resource_type, resource_id, details, ip_address, user_agent) 
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
              logData.user_id, logData.action, logData.resource_type, 
              logData.resource_id, logData.details, logData.ip_address, logData.user_agent
            ]
          ).catch(error => {
            console.error('Activity logging error:', error);
          });
        }

        originalSend.call(this, data);
      };

      next();
    } catch (error) {
      console.error('Activity logging middleware error:', error);
      next();
    }
  };
};
