// فحص قاعدة البيانات
import { executeQuery, getOne, getAll } from './config/databaseSQLite.js';

const checkDatabase = async () => {
  try {
    console.log('🔍 Checking database...');
    
    // فحص الأدوار
    const roles = await getAll('SELECT * FROM roles');
    console.log('📋 Roles:');
    roles.forEach(role => {
      console.log(`- ${role.name}: ${role.permissions}`);
    });

    // فحص المستخدم الإداري
    const admin = await getOne(`
      SELECT u.*, r.name as role_name, r.permissions 
      FROM users u 
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.email = ?
    `, ['<EMAIL>']);
    
    console.log('\n👨‍💼 Admin user:');
    console.log(JSON.stringify(admin, null, 2));

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

checkDatabase();
