// اختبار دالة hasPermission
const hasPermission = (userPermissions, requiredPermission) => {
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return false;
  }
  
  return userPermissions.includes(requiredPermission) || userPermissions.includes('*');
};

console.log('🔍 Testing hasPermission function...');

// اختبار مع صلاحية *
const adminPermissions = ['*'];
console.log('Admin permissions:', adminPermissions);
console.log('Has incidents.read?', hasPermission(adminPermissions, 'incidents.read'));
console.log('Has users.read?', hasPermission(adminPermissions, 'users.read'));

// اختبار مع صلاحيات محددة
const analystPermissions = ['incidents.read', 'incidents.create'];
console.log('\nAnalyst permissions:', analystPermissions);
console.log('Has incidents.read?', hasPermission(analystPermissions, 'incidents.read'));
console.log('Has users.read?', hasPermission(analystPermissions, 'users.read'));

// اختبار مع مصفوفة فارغة
const noPermissions = [];
console.log('\nNo permissions:', noPermissions);
console.log('Has incidents.read?', hasPermission(noPermissions, 'incidents.read'));

// اختبار مع null
console.log('\nNull permissions:');
console.log('Has incidents.read?', hasPermission(null, 'incidents.read'));
