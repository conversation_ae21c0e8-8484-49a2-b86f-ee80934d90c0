// اختبار بسيط جداً
console.log('🔍 Starting simple test...');

fetch('http://localhost:5001/health')
  .then(response => response.json())
  .then(data => {
    console.log('✅ Health check successful:', data);
    
    // تسجيل الدخول
    return fetch('http://localhost:5001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });
  })
  .then(response => response.json())
  .then(data => {
    console.log('✅ Login successful');
    const token = data.data.token;
    
    // اختبار الحوادث
    return fetch('http://localhost:5001/api/incidents', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
  })
  .then(response => {
    console.log('📊 Incidents response status:', response.status);
    return response.json();
  })
  .then(data => {
    if (data.success) {
      console.log('✅ Incidents fetch successful');
      console.log('📋 Incidents count:', data.data.incidents.length);
    } else {
      console.log('❌ Incidents fetch failed:', data.message);
    }
  })
  .catch(error => {
    console.error('💥 Error:', error.message);
  });
