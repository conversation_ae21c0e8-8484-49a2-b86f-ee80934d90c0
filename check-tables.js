// فحص الجداول في قاعدة البيانات
const checkTables = async () => {
  try {
    console.log('🔍 Checking database tables...');
    
    // فحص الجداول الموجودة
    const tablesResponse = await fetch('http://localhost:5001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });

    if (!tablesResponse.ok) {
      console.log('❌ Login failed');
      return;
    }

    const loginData = await tablesResponse.json();
    const token = loginData.data.token;
    console.log('✅ Login successful');

    // محاولة إنشاء حادث جديد لاختبار الجدول
    console.log('\n📋 Testing incident creation...');
    const createResponse = await fetch('http://localhost:5001/api/incidents', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: 'Test Incident',
        title_ar: 'حادث تجريبي',
        description: 'This is a test incident',
        description_ar: 'هذا حادث تجريبي',
        severity: 'medium',
        category: 'security_breach',
        source_ip: '*************',
        target_system: 'Web Server'
      })
    });

    console.log('📊 Create response status:', createResponse.status);
    
    if (createResponse.ok) {
      const createData = await createResponse.json();
      console.log('✅ Incident creation successful');
      console.log('📋 Created incident:', createData.data);
      
      // الآن جرب جلب الحوادث
      console.log('\n📋 Testing incidents fetch after creation...');
      const incidentsResponse = await fetch('http://localhost:5001/api/incidents', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📊 Incidents response status:', incidentsResponse.status);
      
      if (incidentsResponse.ok) {
        const incidentsData = await incidentsResponse.json();
        console.log('✅ Incidents fetch successful');
        console.log('📊 Incidents count:', incidentsData.data.incidents.length);
      } else {
        const errorData = await incidentsResponse.json();
        console.log('❌ Incidents fetch failed:', errorData);
      }
      
    } else {
      const errorData = await createResponse.json();
      console.log('❌ Incident creation failed:', errorData);
    }

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

checkTables();
