// اختبار جلب الحوادث
console.log('🔍 Testing get incidents...');

// تسجيل الدخول أولاً
fetch('http://localhost:5001/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'Admin@123456'
  })
})
.then(response => response.json())
.then(data => {
  if (!data.success) {
    throw new Error('Login failed');
  }
  
  console.log('✅ Login successful');
  const token = data.data.token;
  
  // جلب الحوادث
  return fetch('http://localhost:5001/api/incidents', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
})
.then(response => {
  console.log('📊 Get incidents response status:', response.status);
  return response.json();
})
.then(data => {
  if (data.success) {
    console.log('✅ Incidents retrieved successfully');
    console.log('📊 Incidents count:', data.data.incidents.length);
    if (data.data.incidents.length > 0) {
      console.log('📋 First incident:', data.data.incidents[0]);
    }
  } else {
    console.log('❌ Failed to get incidents:', data.message);
  }
})
.catch(error => {
  console.error('💥 Error:', error.message);
});
