import express from 'express';
import {
  getAllIncidents,
  getIncidentById,
  createIncident,
  updateIncident,
  deleteIncident,
  getIncidentStats
} from '../controllers/incidentController.js';
import {
  validateIncidentCreation,
  validateIncidentUpdate
} from '../validators/incidentValidators.js';
import { authenticateToken, requirePermission, logActivity } from '../middleware/auth.js';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting للحماية من الإفراط في الطلبات
const incidentLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100, // 100 طلب كحد أقصى
  message: {
    success: false,
    message: 'Too many requests, please try again later',
    message_ar: 'طلبات كثيرة جداً، يرجى المحاولة لاحقاً'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);
router.use(incidentLimiter);

// المسارات العامة (تتطلب صلاحية قراءة الحوادث)
router.get('/', requirePermission('incidents.read'), logActivity('incidents_list'), getAllIncidents);
router.get('/stats', requirePermission('incidents.read'), logActivity('incidents_stats'), getIncidentStats);
router.get('/:id', requirePermission('incidents.read'), logActivity('incident_view'), getIncidentById);

// المسارات التي تتطلب صلاحية إنشاء الحوادث
router.post('/', requirePermission('incidents.create'), validateIncidentCreation, logActivity('incident_create'), createIncident);

// المسارات التي تتطلب صلاحية تحديث الحوادث
router.put('/:id', requirePermission('incidents.update'), validateIncidentUpdate, logActivity('incident_update'), updateIncident);

// المسارات التي تتطلب صلاحية حذف الحوادث
router.delete('/:id', requirePermission('incidents.delete'), logActivity('incident_delete'), deleteIncident);

export default router;
