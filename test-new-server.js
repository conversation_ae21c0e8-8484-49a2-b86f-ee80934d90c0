// اختبار الخادم الجديد
const testNewServer = async () => {
  try {
    console.log('🔍 Testing new server...');
    
    // أولاً تسجيل الدخول للحصول على التوكن
    const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });

    if (!loginResponse.ok) {
      const errorData = await loginResponse.json();
      console.log('❌ Login failed:', errorData);
      return;
    }

    const loginData = await loginResponse.json();
    const token = loginData.data.token;
    console.log('✅ Login successful');

    // اختبار endpoint المصادقة
    console.log('\n📋 Testing auth endpoint...');
    const authResponse = await fetch('http://localhost:5002/test-auth', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (authResponse.ok) {
      const authData = await authResponse.json();
      console.log('✅ Auth test successful');
      console.log('📋 Auth data:', JSON.stringify(authData, null, 2));
    } else {
      const errorData = await authResponse.json();
      console.log('❌ Auth test failed:', errorData);
    }

    // اختبار endpoint الصلاحيات
    console.log('\n📋 Testing permission endpoint...');
    const permissionResponse = await fetch('http://localhost:5002/test-permission', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Permission response status:', permissionResponse.status);
    const permissionData = await permissionResponse.json();
    console.log('📋 Permission response:', JSON.stringify(permissionData, null, 2));

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

testNewServer();
