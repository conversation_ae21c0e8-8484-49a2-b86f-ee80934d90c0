// الاختبار النهائي
const finalTest = async () => {
  try {
    console.log('🔍 Final test...');
    
    // تسجيل الدخول
    const loginResponse = await fetch('http://localhost:5001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed');
      return;
    }

    const loginData = await loginResponse.json();
    const token = loginData.data.token;
    console.log('✅ Login successful');

    // اختبار الحوادث
    console.log('\n📋 Testing incidents...');
    const incidentsResponse = await fetch('http://localhost:5001/api/incidents', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Incidents response status:', incidentsResponse.status);
    
    if (incidentsResponse.ok) {
      const incidentsData = await incidentsResponse.json();
      console.log('✅ Incidents fetch successful');
      console.log('📋 Incidents count:', incidentsData.data.incidents.length);
    } else {
      const errorData = await incidentsResponse.json();
      console.log('❌ Incidents fetch failed:', errorData);
    }

    // اختبار إحصائيات الحوادث
    console.log('\n📊 Testing incidents stats...');
    const statsResponse = await fetch('http://localhost:5001/api/incidents/stats', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Stats response status:', statsResponse.status);
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('✅ Stats fetch successful');
      console.log('📊 Total incidents:', statsData.data.total);
    } else {
      const errorData = await statsResponse.json();
      console.log('❌ Stats fetch failed:', errorData);
    }

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

finalTest();
