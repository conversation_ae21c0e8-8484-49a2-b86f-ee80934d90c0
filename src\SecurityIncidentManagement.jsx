import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from './contexts/LanguageContext';
import { useAuth } from './contexts/AuthContext';
import { useNotifications } from './components/NotificationSystem';
import LanguageSwitcher from './components/LanguageSwitcher';
import AdvancedAnalytics from './components/AdvancedAnalytics';
import AdminDashboard from './components/AdminDashboard';
import {
  Shield, Activity, Users, AlertTriangle, CheckCircle2,
  TrendingUp, TrendingDown, Clock, Filter, Plus, Search, MoreVertical,
  ChevronRight, X, Zap, FileWarning, Globe, Server,
  Lock, Mail, Database, Wifi, Eye, Download, Send, Bell
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { motion, AnimatePresence } from 'framer-motion';

const SecurityIncidentManagementContent = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user, logout, hasPermission } = useAuth();
  const { addNotification } = useNotifications();
  const [darkMode, setDarkMode] = useState(true);
  const [showAddIncident, setShowAddIncident] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [filterPriority, setFilterPriority] = useState('all');
  const [animateStats, setAnimateStats] = useState(false);

  useEffect(() => {
    setAnimateStats(true);
    fetchIncidents();
    fetchIncidentStats();
  }, []);

  // جلب الحوادث من API
  const fetchIncidents = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('http://localhost:5001/api/incidents', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setIncidents(data.data.incidents || []);
      } else {
        console.error('Failed to fetch incidents');
        setIncidents([]);
      }
    } catch (error) {
      console.error('Error fetching incidents:', error);
      setIncidents([]);
    } finally {
      setLoading(false);
    }
  };

  // جلب إحصائيات الحوادث من API
  const fetchIncidentStats = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('http://localhost:5001/api/incidents/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setIncidentStats(data.data || {
          total: 0,
          open: 0,
          in_progress: 0,
          resolved: 0,
          closed: 0,
          critical: 0,
          high: 0,
          medium: 0,
          low: 0
        });
      }
    } catch (error) {
      console.error('Error fetching incident stats:', error);
    }
  };

  const [incidents, setIncidents] = useState([]);
  const [incidentStats, setIncidentStats] = useState({
    total: 0,
    open: 0,
    in_progress: 0,
    resolved: 0,
    closed: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0
  });
  const [loading, setLoading] = useState(true);

  const [newIncident, setNewIncident] = useState({
    title: '',
    titleAr: '',
    type: 'malware',
    priority: 'medium',
    assignee: '',
    description: '',
    affectedAssets: [],
    ipAddresses: []
  });

  // Chart data - استخدام البيانات الحقيقية
  const trendData = [
    { time: '00:00', incidents: 0, threats: 0 },
    { time: '04:00', incidents: 0, threats: 0 },
    { time: '08:00', incidents: 0, threats: 0 },
    { time: '12:00', incidents: 0, threats: 0 },
    { time: '16:00', incidents: 0, threats: 0 },
    { time: '20:00', incidents: stats.total || 0, threats: stats.critical || 0 }
  ];

  const severityData = [
    { name: 'Critical', value: incidentStats.critical || 0, color: '#ef4444' },
    { name: 'High', value: incidentStats.high || 0, color: '#f59e0b' },
    { name: 'Medium', value: incidentStats.medium || 0, color: '#3b82f6' },
    { name: 'Low', value: incidentStats.low || 0, color: '#10b981' }
  ];

  const typeData = [
    { type: 'Malware', count: 0 },
    { type: 'Phishing', count: 0 },
    { type: 'DDoS', count: 0 },
    { type: 'Intrusion', count: 0 },
    { type: 'Data Leak', count: 0 }
  ];

  const getStatusColor = (status) => {
    const colors = {
      open: 'bg-red-500',
      in_progress: 'bg-amber-500',
      resolved: 'bg-green-500',
      closed: 'bg-gray-500'
    };
    return colors[status] || 'bg-gray-500';
  };

  const getPriorityIcon = (priority) => {
    switch (priority) {
      case 'critical':
        return <Zap className="w-4 h-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="w-4 h-4 text-amber-500" />;
      case 'medium':
        return <Activity className="w-4 h-4 text-blue-500" />;
      default:
        return <Activity className="w-4 h-4 text-green-500" />;
    }
  };

  const getTypeIcon = (type) => {
    const icons = {
      malware: <FileWarning className="w-5 h-5" />,
      phishing: <Mail className="w-5 h-5" />,
      data_breach: <Database className="w-5 h-5" />,
      unauthorized_access: <Lock className="w-5 h-5" />,
      ddos: <Wifi className="w-5 h-5" />,
      insider_threat: <Users className="w-5 h-5" />,
      other: <Shield className="w-5 h-5" />
    };
    return icons[type] || <Shield className="w-5 h-5" />;
  };

  const stats = {
    total: incidentStats.total || 0,
    active: incidentStats.open || 0,
    critical: incidentStats.critical || 0,
    resolved: incidentStats.resolved || 0,
    avgResponseTime: '0 mins',
    threatLevel: incidentStats.total > 0 ? Math.min(100, (incidentStats.critical * 30 + incidentStats.high * 20 + incidentStats.medium * 10 + incidentStats.low * 5)) : 0
  };

  const filteredIncidents = incidents.filter(incident => {
    const matchesSearch = incident.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incident.title_ar?.includes(searchTerm);
    const matchesPriority = filterPriority === 'all' || incident.severity === filterPriority;
    return matchesSearch && matchesPriority;
  });

  const addIncident = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('http://localhost:5001/api/incidents', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: newIncident.title,
          title_ar: newIncident.titleAr,
          description: newIncident.description,
          description_ar: newIncident.description,
          severity: newIncident.priority,
          category: newIncident.type,
          source_ip: newIncident.ipAddresses[0] || null,
          target_system: newIncident.affectedAssets.join(', ') || null
        })
      });

      if (response.ok) {
        const data = await response.json();

        // إعادة جلب البيانات
        await fetchIncidents();
        await fetchIncidentStats();

        setShowAddIncident(false);
        setNewIncident({
          title: '',
          titleAr: '',
          type: 'malware',
          priority: 'medium',
          assignee: '',
          description: '',
          affectedAssets: [],
          ipAddresses: []
        });

        // Show success notification
        addNotification({
          type: 'success',
          title: t('notifications.incidentCreated'),
          message: data.message || 'Incident created successfully',
          duration: 5000
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create incident');
      }
    } catch (error) {
      console.error('Error creating incident:', error);
      // Show error notification
      addNotification({
        type: 'error',
        title: t('notifications.error'),
        message: error.message,
        duration: 5000
      });
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-950' : 'bg-gray-50'} transition-colors duration-300`}>
      {/* Advanced Header */}
      <div className={`${darkMode ? 'bg-gray-900/50' : 'bg-white'} backdrop-blur-lg border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} sticky top-0 z-40`}>
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl blur-lg opacity-50"></div>
                <div className={`relative ${darkMode ? 'bg-gray-900' : 'bg-white'} p-3 rounded-xl`}>
                  <Shield className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              <div>
                <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {t('header.title')}
                </h1>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {t('header.subtitle')}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                <input
                  type="text"
                  placeholder={t('header.search')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`pl-10 pr-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'}
                            focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 w-64`}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <button
                onClick={() => setShowAddIncident(true)}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg blur opacity-75 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:shadow-lg transition-all duration-200">
                  <Plus className="w-5 h-5" />
                  <span className="font-medium">{t('header.reportIncident')}</span>
                </div>
              </button>

              <LanguageSwitcher darkMode={darkMode} />

              {/* User Info */}
              <div className="flex items-center gap-3">
                <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${
                  darkMode ? 'bg-gray-800' : 'bg-gray-100'
                }`}>
                  <span className="text-lg">{user?.avatar || '👤'}</span>
                  <div className="text-sm">
                    <div className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {user?.name}
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {isRTL ? user?.role?.name_ar : user?.role?.name}
                    </div>
                  </div>
                </div>

                <button
                  onClick={() => setDarkMode(!darkMode)}
                  className={`p-2 rounded-lg ${darkMode ? 'bg-gray-800 text-yellow-400' : 'bg-gray-100 text-gray-700'}
                            hover:shadow-lg transition-all duration-200`}
                >
                  {darkMode ? '🌙' : '☀️'}
                </button>

                <button
                  onClick={logout}
                  className={`p-2 rounded-lg ${darkMode ? 'bg-red-800 text-red-400 hover:bg-red-700' : 'bg-red-100 text-red-700 hover:bg-red-200'}
                            transition-all duration-200`}
                  title={t('auth.logout', 'Logout')}
                >
                  🚪
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className={`${darkMode ? 'bg-gray-900/30' : 'bg-white/50'} backdrop-blur-sm border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex gap-8">
            {['overview', 'incidents', 'analytics', 'team', ...(hasPermission('users.read') ? ['admin'] : [])].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-4 px-2 border-b-2 transition-all duration-200
                  ${activeTab === tab
                    ? `${darkMode ? 'border-blue-500 text-blue-400' : 'border-blue-600 text-blue-600'} font-medium`
                    : `border-transparent ${darkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-600 hover:text-gray-900'}`
                  }`}
              >
                {t(`navigation.${tab}`, tab === 'admin' ? 'Admin' : tab)}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Real-time Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-8">
          {/* Threat Level Indicator */}
          <div className={`${darkMode ? 'bg-gray-900/50' : 'bg-white'} rounded-xl p-6 border ${darkMode ? 'border-gray-800' : 'border-gray-200'}
                        backdrop-blur-sm hover:shadow-xl transition-all duration-300`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{t('stats.threatLevel')}</h3>
              <Activity className={`w-5 h-5 ${darkMode ? 'text-gray-600' : 'text-gray-400'}`} />
            </div>
            <div className="relative h-32">
              <svg className="w-full h-full transform -rotate-90">
                <circle
                  cx="50%"
                  cy="50%"
                  r="45%"
                  stroke={darkMode ? '#1f2937' : '#e5e7eb'}
                  strokeWidth="8"
                  fill="none"
                />
                <circle
                  cx="50%"
                  cy="50%"
                  r="45%"
                  stroke="url(#threatGradient)"
                  strokeWidth="8"
                  fill="none"
                  strokeDasharray={`${stats.threatLevel * 2.83} 283`}
                  className="transition-all duration-1000"
                />
                <defs>
                  <linearGradient id="threatGradient">
                    <stop offset="0%" stopColor="#ef4444" />
                    <stop offset="50%" stopColor="#f59e0b" />
                    <stop offset="100%" stopColor="#10b981" />
                  </linearGradient>
                </defs>
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className={`text-3xl font-bold ${stats.threatLevel > 70 ? 'text-red-500' : stats.threatLevel > 40 ? 'text-amber-500' : 'text-green-500'}`}>
                    {stats.threatLevel}%
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {stats.threatLevel > 70 ? t('stats.critical') : stats.threatLevel > 40 ? t('stats.elevated') : t('stats.normal')}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Active Incidents */}
          <div className={`${darkMode ? 'bg-gray-900/50' : 'bg-white'} rounded-xl p-6 border ${darkMode ? 'border-gray-800' : 'border-gray-200'}
                        backdrop-blur-sm hover:shadow-xl transition-all duration-300 group`}>
            <div className="flex items-center justify-between mb-2">
              <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{t('stats.activeIncidents')}</h3>
              <div className="relative">
                <div className="absolute inset-0 bg-red-500 rounded-full blur opacity-50 group-hover:opacity-75 transition-opacity"></div>
                <AlertTriangle className="w-5 h-5 text-red-500 relative" />
              </div>
            </div>
            <div className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-1`}>
              {animateStats ? stats.active : 0}
            </div>
            <div className="flex items-center gap-2 text-sm">
              {stats.active > 0 ? (
                <>
                  <TrendingUp className="w-4 h-4 text-red-500" />
                  <span className="text-red-500">Active</span>
                  <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>incidents</span>
                </>
              ) : (
                <>
                  <TrendingDown className="w-4 h-4 text-green-500" />
                  <span className="text-green-500">All clear</span>
                  <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>no incidents</span>
                </>
              )}
            </div>
          </div>

          {/* Critical Issues */}
          <div className={`${darkMode ? 'bg-gray-900/50' : 'bg-white'} rounded-xl p-6 border ${darkMode ? 'border-gray-800' : 'border-gray-200'}
                        backdrop-blur-sm hover:shadow-xl transition-all duration-300`}>
            <div className="flex items-center justify-between mb-2">
              <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Critical Issues</h3>
              <Zap className={`w-5 h-5 ${darkMode ? 'text-amber-400' : 'text-amber-500'}`} />
            </div>
            <div className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-1`}>
              {animateStats ? stats.critical : 0}
            </div>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {stats.critical > 0 ? 'Requires immediate action' : 'No critical issues'}
            </div>
          </div>

          {/* Response Time */}
          <div className={`${darkMode ? 'bg-gray-900/50' : 'bg-white'} rounded-xl p-6 border ${darkMode ? 'border-gray-800' : 'border-gray-200'}
                        backdrop-blur-sm hover:shadow-xl transition-all duration-300`}>
            <div className="flex items-center justify-between mb-2">
              <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Avg Response</h3>
              <Clock className={`w-5 h-5 ${darkMode ? 'text-blue-400' : 'text-blue-500'}`} />
            </div>
            <div className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-1`}>
              {stats.avgResponseTime}
            </div>
            <div className="flex items-center gap-2 text-sm">
              {stats.active > 0 ? (
                <>
                  <span className="text-amber-500">Calculating...</span>
                  <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>response time</span>
                </>
              ) : (
                <>
                  <span className="text-green-500">No incidents</span>
                  <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>to respond to</span>
                </>
              )}
            </div>
          </div>

          {/* Team Status */}
          <div className={`${darkMode ? 'bg-gray-900/50' : 'bg-white'} rounded-xl p-6 border ${darkMode ? 'border-gray-800' : 'border-gray-200'}
                        backdrop-blur-sm hover:shadow-xl transition-all duration-300`}>
            <div className="flex items-center justify-between mb-2">
              <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Team Online</h3>
              <Users className={`w-5 h-5 ${darkMode ? 'text-green-400' : 'text-green-500'}`} />
            </div>
            <div className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-1`}>
              1/1
            </div>
            <div className="flex -space-x-2">
              <div className={`w-8 h-8 rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}
                            flex items-center justify-center text-sm border-2 ${darkMode ? 'border-gray-900' : 'border-white'}`}>
                👤
              </div>
            </div>
          </div>

          {/* Security Score */}
          <div className={`${darkMode ? 'bg-gray-900/50' : 'bg-white'} rounded-xl p-6 border ${darkMode ? 'border-gray-800' : 'border-gray-200'}
                        backdrop-blur-sm hover:shadow-xl transition-all duration-300`}>
            <div className="flex items-center justify-between mb-2">
              <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Security Score</h3>
              <Shield className={`w-5 h-5 ${darkMode ? 'text-purple-400' : 'text-purple-500'}`} />
            </div>
            <div className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-1`}>
              {stats.critical === 0 && stats.active === 0 ? 'A+' : stats.critical > 0 ? 'C' : stats.active > 0 ? 'B' : 'A+'}
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-800 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${
                  stats.critical === 0 && stats.active === 0
                    ? 'bg-gradient-to-r from-green-500 to-blue-500'
                    : stats.critical > 0
                    ? 'bg-gradient-to-r from-red-500 to-orange-500'
                    : 'bg-gradient-to-r from-yellow-500 to-blue-500'
                }`}
                style={{
                  width: `${stats.critical === 0 && stats.active === 0 ? '100%' : stats.critical > 0 ? '40%' : '70%'}`
                }}
              ></div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        {activeTab === 'analytics' && (
          <AdvancedAnalytics darkMode={darkMode} isRTL={isRTL} />
        )}

        {activeTab === 'admin' && hasPermission('users.read') && (
          <AdminDashboard darkMode={darkMode} />
        )}

        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Incident Trend Chart */}
            <div className={`lg:col-span-2 ${darkMode ? 'bg-gray-900/50' : 'bg-white'} rounded-xl p-6 border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
              <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Incident & Threat Trend
              </h3>
              {stats.total === 0 ? (
                <div className="h-64 flex items-center justify-center">
                  <div className="text-center">
                    <div className={`mb-4 ${darkMode ? 'text-gray-600' : 'text-gray-400'}`}>
                      <Activity className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    </div>
                    <h4 className={`text-lg font-semibold mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      No Incident Data
                    </h4>
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Charts will appear here once incidents are reported
                    </p>
                  </div>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={250}>
                  <AreaChart data={trendData}>
                    <defs>
                      <linearGradient id="incidentGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                      </linearGradient>
                      <linearGradient id="threatGradientChart" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#ef4444" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#ef4444" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#e5e7eb'} />
                    <XAxis dataKey="time" stroke={darkMode ? '#9ca3af' : '#6b7280'} />
                    <YAxis stroke={darkMode ? '#9ca3af' : '#6b7280'} />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: darkMode ? '#1f2937' : '#ffffff',
                        border: `1px solid ${darkMode ? '#374151' : '#e5e7eb'}`,
                        borderRadius: '8px'
                      }}
                    />
                    <Area type="monotone" dataKey="incidents" stroke="#3b82f6" fillOpacity={1} fill="url(#incidentGradient)" strokeWidth={2} />
                    <Area type="monotone" dataKey="threats" stroke="#ef4444" fillOpacity={1} fill="url(#threatGradientChart)" strokeWidth={2} />
                  </AreaChart>
                </ResponsiveContainer>
              )}
            </div>

            {/* Severity Distribution */}
            <div className={`${darkMode ? 'bg-gray-900/50' : 'bg-white'} rounded-xl p-6 border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
              <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Severity Distribution
              </h3>
              {stats.total === 0 ? (
                <div className="h-64 flex items-center justify-center">
                  <div className="text-center">
                    <div className={`mb-4 ${darkMode ? 'text-gray-600' : 'text-gray-400'}`}>
                      <Shield className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    </div>
                    <h4 className={`text-lg font-semibold mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      No Severity Data
                    </h4>
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Severity distribution will appear here
                    </p>
                  </div>
                </div>
              ) : (
                <>
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={severityData.filter(item => item.value > 0)}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {severityData.filter(item => item.value > 0).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: darkMode ? '#1f2937' : '#ffffff',
                          border: `1px solid ${darkMode ? '#374151' : '#e5e7eb'}`,
                          borderRadius: '8px'
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                  <div className="grid grid-cols-2 gap-2 mt-4">
                    {severityData.map((item) => (
                      <div key={item.name} className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                        <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {item.name}: {item.value}
                        </span>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Incidents List */}
        {(activeTab === 'incidents' || activeTab === 'overview') && (
          <div className={`mt-6 ${darkMode ? 'bg-gray-900/50' : 'bg-white'} rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} overflow-hidden`}>
            <div className={`p-6 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
              <div className="flex items-center justify-between">
                <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Active Incidents
                </h3>
                <div className="flex items-center gap-2">
                  {['all', 'critical', 'high', 'medium', 'low'].map((priority) => (
                    <button
                      key={priority}
                      onClick={() => setFilterPriority(priority)}
                      className={`px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200
                        ${filterPriority === priority
                          ? 'bg-blue-500 text-white'
                          : `${darkMode ? 'bg-gray-800 text-gray-400 hover:bg-gray-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`
                        }`}
                    >
                      {priority.charAt(0).toUpperCase() + priority.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div className={`divide-y ${darkMode ? 'divide-gray-800' : 'divide-gray-200'}`}>
              {loading ? (
                <div className="p-12 text-center">
                  <div className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Loading incidents...
                  </div>
                </div>
              ) : filteredIncidents.length === 0 ? (
                <div className="p-12 text-center">
                  <div className={`mb-4 ${darkMode ? 'text-gray-600' : 'text-gray-400'}`}>
                    <Shield className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  </div>
                  <h3 className={`text-xl font-semibold mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    No Security Incidents
                  </h3>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-6`}>
                    Great! No security incidents have been reported yet. Your system is secure.
                  </p>
                  <button
                    onClick={() => setShowAddIncident(true)}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg
                             font-medium hover:shadow-lg transition-all flex items-center gap-2 mx-auto"
                  >
                    <Plus className="w-5 h-5" />
                    Report First Incident
                  </button>
                </div>
              ) : (
                filteredIncidents.map((incident, index) => (
                <div
                  key={incident.id}
                  className={`p-6 hover:${darkMode ? 'bg-gray-800/50' : 'bg-gray-50'} transition-all duration-200 cursor-pointer group`}
                  onClick={() => setSelectedIncident(incident)}
                  style={{
                    animation: `slideIn 0.3s ease-out ${index * 0.1}s backwards`
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4">
                      <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} group-hover:scale-110 transition-transform duration-200`}>
                        {getTypeIcon(incident.category)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} group-hover:text-blue-500 transition-colors`}>
                            {incident.title}
                          </h4>
                          <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(incident.status)} bg-opacity-20`}>
                            {incident.status}
                          </span>
                          {getPriorityIcon(incident.severity)}
                        </div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2 line-clamp-2`}>
                          {incident.description}
                        </p>
                        <div className="flex items-center gap-4 text-sm">
                          <div className="flex items-center gap-1">
                            <Globe className={`w-4 h-4 ${darkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                            <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>{incident.source_ip || 'Unknown'}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Server className={`w-4 h-4 ${darkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                            <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
                              {incident.target_system || 'No assets specified'}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className={`w-4 h-4 ${darkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                            <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
                              {new Date(incident.created_at).toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-right">
                        <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>Created by</div>
                        <div className="flex items-center gap-2">
                          <span className="text-lg">👤</span>
                          <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {incident.created_by_name || 'System'}
                          </span>
                        </div>
                      </div>
                      <button className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'} transition-colors`}>
                        <MoreVertical className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`} />
                      </button>
                    </div>
                  </div>

                  {/* Status Bar */}
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-1">
                      <span className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-600'}`}>
                        Status: {incident.status}
                      </span>
                      <span className={`text-xs font-medium ${incident.severity === 'critical' ? 'text-red-500' : incident.severity === 'high' ? 'text-amber-500' : incident.severity === 'medium' ? 'text-blue-500' : 'text-green-500'}`}>
                        Severity: {incident.severity}
                      </span>
                    </div>
                    <div className={`w-full h-1.5 ${darkMode ? 'bg-gray-800' : 'bg-gray-200'} rounded-full overflow-hidden`}>
                      <div
                        className={`h-full transition-all duration-500 ${
                          incident.status === 'resolved' ? 'bg-green-500' :
                          incident.status === 'in_progress' ? 'bg-blue-500' :
                          incident.status === 'closed' ? 'bg-gray-500' : 'bg-red-500'
                        }`}
                        style={{
                          width: `${
                            incident.status === 'resolved' ? '100%' :
                            incident.status === 'in_progress' ? '60%' :
                            incident.status === 'closed' ? '100%' : '20%'
                          }`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
                ))
              )}
            </div>
          </div>
        )}

        {/* Add Incident Modal */}
        {showAddIncident && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className={`${darkMode ? 'bg-gray-900' : 'bg-white'} rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl`}>
              <div className={`p-6 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                <div className="flex items-center justify-between">
                  <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Report New Security Incident
                  </h2>
                  <button
                    onClick={() => setShowAddIncident(false)}
                    className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'} transition-colors`}
                  >
                    <X className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`} />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Incident Title (English)
                    </label>
                    <input
                      type="text"
                      value={newIncident.title}
                      onChange={(e) => setNewIncident({...newIncident, title: e.target.value})}
                      className={`w-full px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'}
                                focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all`}
                      placeholder="Enter incident title"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      العنوان (عربي)
                    </label>
                    <input
                      type="text"
                      value={newIncident.titleAr}
                      onChange={(e) => setNewIncident({...newIncident, titleAr: e.target.value})}
                      className={`w-full px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'}
                                focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all`}
                      placeholder="أدخل عنوان الحادث"
                      dir="rtl"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Incident Type
                    </label>
                    <select
                      value={newIncident.type}
                      onChange={(e) => setNewIncident({...newIncident, type: e.target.value})}
                      className={`w-full px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'}
                                focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all`}
                    >
                      <option value="malware">Malware</option>
                      <option value="phishing">Phishing</option>
                      <option value="data_breach">Data Breach</option>
                      <option value="unauthorized_access">Unauthorized Access</option>
                      <option value="ddos">DDoS Attack</option>
                      <option value="insider_threat">Insider Threat</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Priority Level
                    </label>
                    <select
                      value={newIncident.priority}
                      onChange={(e) => setNewIncident({...newIncident, priority: e.target.value})}
                      className={`w-full px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'}
                                focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all`}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="critical">Critical</option>
                    </select>
                  </div>
                </div>



                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Description
                  </label>
                  <textarea
                    value={newIncident.description}
                    onChange={(e) => setNewIncident({...newIncident, description: e.target.value})}
                    rows="4"
                    className={`w-full px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'}
                              focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all resize-none`}
                    placeholder="Provide detailed description of the incident..."
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Affected Assets
                    </label>
                    <input
                      type="text"
                      value={newIncident.affectedAssets.join(', ')}
                      onChange={(e) => setNewIncident({...newIncident, affectedAssets: e.target.value.split(',').map(s => s.trim())})}
                      className={`w-full px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'}
                                focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all`}
                      placeholder="Server names, systems (comma separated)"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      IP Addresses
                    </label>
                    <input
                      type="text"
                      value={newIncident.ipAddresses.join(', ')}
                      onChange={(e) => setNewIncident({...newIncident, ipAddresses: e.target.value.split(',').map(s => s.trim())})}
                      className={`w-full px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'}
                                focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all`}
                      placeholder="Suspicious IPs (comma separated)"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <button
                    onClick={() => setShowAddIncident(false)}
                    className={`px-6 py-2 rounded-lg ${darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-200 hover:bg-gray-300'}
                              transition-colors font-medium`}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={addIncident}
                    disabled={!newIncident.title || !newIncident.description}
                    className="relative group disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg blur opacity-75
                                  group-hover:opacity-100 transition-opacity"></div>
                    <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg
                                  font-medium flex items-center gap-2">
                      <Send className="w-4 h-4" />
                      Submit Incident
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Incident Details Modal */}
        {selectedIncident && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className={`${darkMode ? 'bg-gray-900' : 'bg-white'} rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl`}>
              <div className={`sticky top-0 p-6 border-b ${darkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-white'} z-10`}>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-3 mb-2">
                      <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {selectedIncident.title}
                      </h2>
                      <span className={`text-sm px-3 py-1 rounded-full font-medium ${getStatusColor(selectedIncident.status)} bg-opacity-20`}>
                        {selectedIncident.status}
                      </span>
                      {getPriorityIcon(selectedIncident.priority)}
                    </div>
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Incident ID: {selectedIncident.id}
                    </p>
                  </div>
                  <button
                    onClick={() => setSelectedIncident(null)}
                    className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'} transition-colors`}
                  >
                    <X className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`} />
                  </button>
                </div>
              </div>

              <div className="p-6">
                {/* Threat Analysis */}
                <div className={`mb-6 p-4 rounded-lg ${darkMode ? 'bg-gray-800/50' : 'bg-gray-50'}`}>
                  <h3 className={`font-semibold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Threat Analysis
                  </h3>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>Threat Level</div>
                      <div className="flex items-center gap-2">
                        <div className={`text-2xl font-bold ${selectedIncident.threatLevel > 80 ? 'text-red-500' : selectedIncident.threatLevel > 50 ? 'text-amber-500' : 'text-green-500'}`}>
                          {selectedIncident.threatLevel}%
                        </div>
                        <div className={`w-full h-2 ${darkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded-full overflow-hidden`}>
                          <div
                            className={`h-full transition-all duration-500 ${
                              selectedIncident.threatLevel > 80 ? 'bg-red-500' :
                              selectedIncident.threatLevel > 50 ? 'bg-amber-500' : 'bg-green-500'
                            }`}
                            style={{ width: `${selectedIncident.threatLevel}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>Attack Origin</div>
                      <div className="flex items-center gap-2">
                        <Globe className="w-4 h-4 text-blue-500" />
                        <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {selectedIncident.location}
                        </span>
                      </div>
                    </div>
                    <div>
                      <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>Incident Type</div>
                      <div className="flex items-center gap-2">
                        {getTypeIcon(selectedIncident.type)}
                        <span className={`font-medium capitalize ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {selectedIncident.type}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-6">
                  <h3 className={`font-semibold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Description
                  </h3>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} leading-relaxed`}>
                    {selectedIncident.description}
                  </p>
                </div>

                {/* Affected Systems */}
                <div className="mb-6">
                  <h3 className={`font-semibold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Affected Systems & Assets
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>Systems</div>
                      <div className="flex flex-wrap gap-2">
                        {selectedIncident.affectedAssets.map((asset, index) => (
                          <span key={index} className={`px-3 py-1 rounded-lg text-sm ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                            <Server className="w-3 h-3 inline mr-1" />
                            {asset}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>IP Addresses</div>
                      <div className="flex flex-wrap gap-2">
                        {selectedIncident.ipAddresses.map((ip, index) => (
                          <span key={index} className={`px-3 py-1 rounded-lg text-sm font-mono ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                            {ip}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Response Timeline */}
                <div className="mb-6">
                  <h3 className={`font-semibold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Response Timeline
                  </h3>
                  <div className="space-y-3">
                    {selectedIncident.actions.map((action, index) => (
                      <div key={action.id} className="flex items-start gap-3">
                        <div className="relative">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            action.status === 'completed' ? 'bg-green-500' : 'bg-amber-500'
                          }`}>
                            {action.status === 'completed' ? (
                              <CheckCircle2 className="w-4 h-4 text-white" />
                            ) : (
                              <Clock className="w-4 h-4 text-white" />
                            )}
                          </div>
                          {index < selectedIncident.actions.length - 1 && (
                            <div className={`absolute top-8 left-4 w-0.5 h-12 ${darkMode ? 'bg-gray-700' : 'bg-gray-300'}`}></div>
                          )}
                        </div>
                        <div className="flex-1">
                          <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {action.action}
                          </p>
                          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {action.time || 'In progress'}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className={`flex items-center justify-between pt-4 border-t ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                  <div className="flex gap-3">
                    <button className={`px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-100 hover:bg-gray-200'}
                                    transition-colors font-medium flex items-center gap-2`}>
                      <Download className="w-4 h-4" />
                      Export Report
                    </button>
                    <button className={`px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-100 hover:bg-gray-200'}
                                    transition-colors font-medium flex items-center gap-2`}>
                      <Eye className="w-4 h-4" />
                      View Logs
                    </button>
                  </div>
                  <button
                    onClick={() => setSelectedIncident(null)}
                    className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg
                             font-medium hover:shadow-lg transition-all"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};

export default SecurityIncidentManagementContent;