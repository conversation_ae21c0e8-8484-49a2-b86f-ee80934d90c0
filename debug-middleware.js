// إضافة تصحيح أخطاء إلى middleware
const testMiddleware = async () => {
  try {
    console.log('🔍 Testing middleware debug...');
    
    // أولاً تسجيل الدخول للحصول على التوكن
    const loginResponse = await fetch('http://localhost:5001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });

    if (!loginResponse.ok) {
      throw new Error('Login failed');
    }

    const loginData = await loginResponse.json();
    const token = loginData.data.token;
    console.log('✅ Login successful, got token');

    // اختبار endpoint بسيط أولاً
    console.log('\n📋 Testing simple endpoint...');
    const profileResponse = await fetch('http://localhost:5001/api/auth/profile', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Profile response status:', profileResponse.status);
    
    if (profileResponse.ok) {
      const profileData = await profileResponse.json();
      console.log('✅ Profile fetch successful');
    } else {
      const errorData = await profileResponse.json();
      console.log('❌ Profile error:', errorData);
    }

    // الآن اختبار الحوادث
    console.log('\n📋 Testing incidents endpoint...');
    const incidentsResponse = await fetch('http://localhost:5001/api/incidents', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Incidents response status:', incidentsResponse.status);
    
    const incidentsData = await incidentsResponse.json();
    console.log('📋 Incidents response:', JSON.stringify(incidentsData, null, 2));

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

testMiddleware();
