# اختبار تسجيل الدخول
$body = @{
    email = '<EMAIL>'
    password = 'Admin@123456'
} | ConvertTo-Json

try {
    Write-Host "🔍 Testing login..."
    $response = Invoke-RestMethod -Uri 'http://localhost:5001/api/auth/login' -Method POST -ContentType 'application/json' -Body $body
    Write-Host "✅ Login successful"
    Write-Host "👤 User: $($response.data.user.name)"
    Write-Host "🎭 Role: $($response.data.user.role.name)"
    
    $token = $response.data.token
    Write-Host "🔑 Token received"
    
    # اختبار الحوادث
    Write-Host "`n📋 Testing incidents..."
    $headers = @{
        'Authorization' = "Bearer $token"
        'Content-Type' = 'application/json'
    }
    
    $incidentsResponse = Invoke-RestMethod -Uri 'http://localhost:5001/api/incidents' -Method GET -Headers $headers
    Write-Host "✅ Incidents fetch successful"
    Write-Host "📊 Incidents count: $($incidentsResponse.data.incidents.Count)"
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)"
}
