// فحص الصلاحيات في قاعدة البيانات
import { executeQuery, getOne, getAll } from './server/config/databaseSQLite.js';

const checkPermissions = async () => {
  try {
    console.log('🔍 Checking database permissions...');
    
    // فحص الأدوار
    const roles = await getAll('SELECT * FROM roles');
    console.log('📋 Roles in database:');
    roles.forEach(role => {
      console.log(`- ${role.name}: ${role.permissions}`);
    });

    // فحص المستخدمين
    const users = await getAll(`
      SELECT u.*, r.name as role_name, r.permissions 
      FROM users u 
      LEFT JOIN roles r ON u.role_id = r.id
    `);
    console.log('\n👥 Users in database:');
    users.forEach(user => {
      console.log(`- ${user.email} (${user.role_name}): ${user.permissions}`);
    });

    // فحص المستخدم الإداري
    const admin = await getOne(`
      SELECT u.*, r.name as role_name, r.permissions 
      FROM users u 
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.email = ?
    `, ['<EMAIL>']);
    
    console.log('\n👨‍💼 Admin user details:');
    console.log(JSON.stringify(admin, null, 2));

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

checkPermissions();
