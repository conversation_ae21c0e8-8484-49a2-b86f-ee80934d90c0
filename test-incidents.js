// اختبار جلب الحوادث
const testIncidents = async () => {
  try {
    console.log('🔍 Testing incidents API...');
    
    // أولاً تسجيل الدخول للحصول على التوكن
    const loginResponse = await fetch('http://localhost:5001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });

    if (!loginResponse.ok) {
      throw new Error('Login failed');
    }

    const loginData = await loginResponse.json();
    const token = loginData.data.token;
    console.log('✅ Login successful, got token');

    // اختبار جلب الحوادث
    console.log('📋 Testing incidents fetch...');
    const incidentsResponse = await fetch('http://localhost:5001/api/incidents', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Incidents response status:', incidentsResponse.status);
    
    const incidentsData = await incidentsResponse.json();
    console.log('📋 Incidents data:', JSON.stringify(incidentsData, null, 2));

    // اختبار جلب إحصائيات الحوادث
    console.log('📊 Testing incidents stats...');
    const statsResponse = await fetch('http://localhost:5001/api/incidents/stats', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Stats response status:', statsResponse.status);
    
    const statsData = await statsResponse.json();
    console.log('📊 Stats data:', JSON.stringify(statsData, null, 2));

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

testIncidents();
