// إصلاح الصلاحيات في قاعدة البيانات
import { executeQuery, getOne, getAll } from './config/databaseSQLite.js';

const fixPermissions = async () => {
  try {
    console.log('🔧 Fixing permissions...');
    
    // تحديث صلاحيات دور المدير الأعلى
    const superAdminPermissions = ['*'];
    
    await executeQuery(
      'UPDATE roles SET permissions = ? WHERE name = ?',
      [JSON.stringify(superAdminPermissions), 'super_admin']
    );
    
    console.log('✅ Updated super_admin permissions to:', superAdminPermissions);
    
    // التحقق من التحديث
    const role = await getOne('SELECT * FROM roles WHERE name = ?', ['super_admin']);
    console.log('📋 Super admin role after update:', role);
    
    // التحقق من المستخدم الإداري
    const admin = await getOne(`
      SELECT u.*, r.name as role_name, r.permissions 
      FROM users u 
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.email = ?
    `, ['<EMAIL>']);
    
    console.log('👨‍💼 Admin user details:', admin);
    
    console.log('✅ Permissions fixed successfully!');
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

fixPermissions();
