// اختبار بيانات المستخدم
const testUserData = async () => {
  try {
    console.log('🔍 Testing user data...');
    
    // أولاً تسجيل الدخول للحصول على التوكن
    const loginResponse = await fetch('http://localhost:5001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });

    if (!loginResponse.ok) {
      throw new Error('Login failed');
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    console.log('👤 User data from login:', JSON.stringify(loginData.data.user, null, 2));

    const token = loginData.data.token;

    // اختبار جلب الملف الشخصي
    console.log('\n📋 Testing profile fetch...');
    const profileResponse = await fetch('http://localhost:5001/api/auth/profile', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (profileResponse.ok) {
      const profileData = await profileResponse.json();
      console.log('👤 Profile data:', JSON.stringify(profileData.data, null, 2));
    } else {
      console.log('❌ Profile fetch failed:', profileResponse.status);
    }

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

testUserData();
