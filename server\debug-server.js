// خادم تصحيح الأخطاء
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { initDatabase, testConnection } from './config/databaseSQLite.js';
import authRoutes from './routes/authSQLite.js';
import incidentRoutes from './routes/incidents.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5001;

// إعداد CORS
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// إعداد middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// إضافة middleware للسجلات
app.use((req, res, next) => {
  console.log(`📝 ${new Date().toISOString()} - ${req.method} ${req.path}`);
  if (req.headers.authorization) {
    console.log(`🔑 Authorization header present: ${req.headers.authorization.substring(0, 20)}...`);
  }
  next();
});

// مسار الصحة
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Debug server is running',
    timestamp: new Date().toISOString()
  });
});

// المسارات
app.use('/api/auth', authRoutes);
app.use('/api/incidents', incidentRoutes);

// معالج الأخطاء
app.use((error, req, res, next) => {
  console.error('💥 Server error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// بدء الخادم
const startServer = async () => {
  try {
    console.log('🔗 Initializing SQLite database...');
    await initDatabase();
    
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ Failed to connect to SQLite database.');
      process.exit(1);
    }

    app.listen(PORT, () => {
      console.log('\n🚀 Debug Server Started');
      console.log(`📍 Server running on: http://localhost:${PORT}`);
      console.log(`📊 Health Check: http://localhost:${PORT}/health`);
      console.log('✅ Server is ready to accept connections\n');
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
