// اختبار مفصل للصلاحيات
const testDetailed = async () => {
  try {
    console.log('🔍 Testing detailed permissions...');
    
    // أولاً تسجيل الدخول للحصول على التوكن
    const loginResponse = await fetch('http://localhost:5001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });

    if (!loginResponse.ok) {
      const errorData = await loginResponse.json();
      console.log('❌ Login failed:', errorData);
      return;
    }

    const loginData = await loginResponse.json();
    const token = loginData.data.token;
    console.log('✅ Login successful');
    console.log('👤 User permissions:', loginData.data.user.role.permissions);

    // فك تشفير التوكن لرؤية محتواه
    const tokenParts = token.split('.');
    const payload = JSON.parse(atob(tokenParts[1]));
    console.log('🔑 Token payload:', payload);

    // اختبار الملف الشخصي
    console.log('\n📋 Testing profile...');
    const profileResponse = await fetch('http://localhost:5001/api/auth/profile', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (profileResponse.ok) {
      const profileData = await profileResponse.json();
      console.log('✅ Profile successful');
      console.log('👤 Profile permissions:', profileData.data.role.permissions);
    } else {
      const errorData = await profileResponse.json();
      console.log('❌ Profile failed:', errorData);
    }

    // اختبار الأدوار
    console.log('\n📋 Testing roles...');
    const rolesResponse = await fetch('http://localhost:5001/api/roles', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Roles response status:', rolesResponse.status);
    const rolesData = await rolesResponse.json();
    console.log('📋 Roles response:', JSON.stringify(rolesData, null, 2));

    // اختبار الحوادث
    console.log('\n📋 Testing incidents...');
    const incidentsResponse = await fetch('http://localhost:5001/api/incidents', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Incidents response status:', incidentsResponse.status);
    const incidentsData = await incidentsResponse.json();
    console.log('📋 Incidents response:', JSON.stringify(incidentsData, null, 2));

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

testDetailed();
