// اختبار أساسي
console.log('🔍 Starting basic test...');

// اختبار الصحة
fetch('http://localhost:5001/health')
  .then(response => {
    console.log('📊 Health response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('✅ Health check successful');
    console.log('📋 Health data:', data);
    
    // اختبار تسجيل الدخول
    console.log('\n🔐 Testing login...');
    return fetch('http://localhost:5001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });
  })
  .then(response => {
    console.log('📊 Login response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('📋 Login response data:', data);
    
    if (data.success && data.data && data.data.token) {
      console.log('✅ Login successful');
      const token = data.data.token;
      
      // اختبار الحوادث
      console.log('\n📋 Testing incidents...');
      return fetch('http://localhost:5001/api/incidents', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
    } else {
      console.log('❌ Login failed');
      throw new Error('Login failed');
    }
  })
  .then(response => {
    console.log('📊 Incidents response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('📋 Incidents response data:', data);
    
    if (data.success) {
      console.log('✅ Incidents API working');
      console.log(`📊 Incidents count: ${data.data.incidents.length}`);
    } else {
      console.log('❌ Incidents API failed');
    }
  })
  .catch(error => {
    console.error('💥 Error:', error.message);
  });
