// اختبار تسجيل الدخول
const testLogin = async () => {
  try {
    console.log('🔍 Testing login...');
    
    const response = await fetch('http://localhost:5001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });

    console.log('📊 Response status:', response.status);
    
    const data = await response.json();
    console.log('📋 Response data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('✅ Login successful!');
      console.log('🔑 Token:', data.data.token.substring(0, 50) + '...');
      console.log('👤 User:', data.data.user.name);
      console.log('🎭 Role:', data.data.user.role.name);
    } else {
      console.log('❌ Login failed:', data.message);
    }

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

testLogin();
