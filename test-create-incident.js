// اختبار إنشاء حادث
console.log('🔍 Testing incident creation...');

// تسجيل الدخول أولاً
fetch('http://localhost:5001/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'Admin@123456'
  })
})
.then(response => response.json())
.then(data => {
  if (!data.success) {
    throw new Error('Login failed');
  }
  
  console.log('✅ Login successful');
  const token = data.data.token;
  
  // إنشاء حادث جديد
  return fetch('http://localhost:5001/api/incidents', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      title: 'Test Security Incident',
      title_ar: 'حادث أمني تجريبي',
      description: 'This is a test security incident for verification',
      description_ar: 'هذا حادث أمني تجريبي للتحقق',
      priority: 'medium',
      type: 'data',
      source_ip: '*************',
      target_system: 'Web Server'
    })
  });
})
.then(response => {
  console.log('📊 Create incident response status:', response.status);
  return response.json();
})
.then(data => {
  if (data.success) {
    console.log('✅ Incident created successfully');
    console.log('📋 Incident ID:', data.data.id);
    console.log('📋 Incident title:', data.data.title);
  } else {
    console.log('❌ Incident creation failed:', data.message);
  }
})
.catch(error => {
  console.error('💥 Error:', error.message);
});
