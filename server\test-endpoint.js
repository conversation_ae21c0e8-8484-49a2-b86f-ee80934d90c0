// endpoint اختبار بسيط
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { initDatabase, testConnection } from './config/databaseSQLite.js';
import { authenticateToken, requirePermission } from './middleware/authSQLite.js';
import authRoutes from './routes/authSQLite.js';

dotenv.config();

const app = express();
const PORT = 5002;

// إعداد CORS
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// إعداد middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// مسار الصحة
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Test server is running',
    timestamp: new Date().toISOString()
  });
});

// مسار اختبار بسيط
app.get('/test-auth', authenticateToken, (req, res) => {
  console.log('🔍 Test auth endpoint called');
  console.log('- User ID:', req.user.id);
  console.log('- User email:', req.user.email);
  console.log('- User role:', req.user.role_name);
  console.log('- User permissions (raw):', req.user.permissions);
  console.log('- User permissions type:', typeof req.user.permissions);
  
  let parsedPermissions = [];
  try {
    if (req.user.permissions) {
      if (typeof req.user.permissions === 'string') {
        parsedPermissions = JSON.parse(req.user.permissions);
      } else if (Array.isArray(req.user.permissions)) {
        parsedPermissions = req.user.permissions;
      }
    }
  } catch (error) {
    console.error('Error parsing permissions:', error);
  }
  
  console.log('- Parsed permissions:', parsedPermissions);
  
  res.json({
    success: true,
    user: {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role_name,
      permissions_raw: req.user.permissions,
      permissions_parsed: parsedPermissions
    }
  });
});

// مسار اختبار الصلاحيات
app.get('/test-permission', authenticateToken, requirePermission('incidents.read'), (req, res) => {
  console.log('🎉 Permission test passed!');
  res.json({
    success: true,
    message: 'Permission test passed',
    required_permission: 'incidents.read'
  });
});

// المسارات
app.use('/api/auth', authRoutes);

// بدء الخادم
const startServer = async () => {
  try {
    console.log('🔗 Initializing SQLite database...');
    await initDatabase();
    
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ Failed to connect to SQLite database.');
      process.exit(1);
    }

    app.listen(PORT, () => {
      console.log('\n🚀 Test Server Started');
      console.log(`📍 Server running on: http://localhost:${PORT}`);
      console.log(`📊 Health Check: http://localhost:${PORT}/health`);
      console.log(`🔍 Test Auth: http://localhost:${PORT}/test-auth`);
      console.log(`🔐 Test Permission: http://localhost:${PORT}/test-permission`);
      console.log('✅ Server is ready to accept connections\n');
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
