// اختبار بسيط للحوادث
const testIncidents = async () => {
  try {
    console.log('🔍 Testing incidents...');
    
    // تسجيل الدخول
    const loginResponse = await fetch('http://localhost:5001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed');
      return;
    }

    const loginData = await loginResponse.json();
    console.log('📋 Login data:', JSON.stringify(loginData, null, 2));

    if (!loginData.data || !loginData.data.token) {
      console.log('❌ No token in response');
      return;
    }

    const token = loginData.data.token;
    console.log('✅ Login successful');

    // اختبار الحوادث
    console.log('\n📋 Testing incidents...');
    const incidentsResponse = await fetch('http://localhost:5001/api/incidents', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Response status:', incidentsResponse.status);
    console.log('📊 Response headers:', Object.fromEntries(incidentsResponse.headers));

    if (incidentsResponse.ok) {
      const incidentsData = await incidentsResponse.json();
      console.log('✅ Incidents successful');
      console.log('📋 Data:', JSON.stringify(incidentsData, null, 2));
    } else {
      const errorText = await incidentsResponse.text();
      console.log('❌ Incidents failed');
      console.log('📋 Error text:', errorText);
    }

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

testIncidents();
